//
//  URLParsingTests.swift
//  Pebl
//
//  Created by AI Assistant on 6/25/25.
//

import Foundation
@testable import Pebl

/// Comprehensive tests for URL parsing functionality
class URLParsingTests {
    
    static func runAllURLParsingTests() {
        print("🧪 URL PARSING TESTS")
        print(String(repeating: "=", count: 60))
        print("Testing URL parsing for different categories and websites...")
        
        testMovieURLs()
        testShoppingURLs()
        testBookURLs()
        testGenericURLs()
        testEdgeCaseURLs()
        
        print("\n✅ URL PARSING TESTS COMPLETED")
    }
    
    static func testMovieURLs() {
        print("\n🎬 Testing Movie URLs")
        print(String(repeating: "-", count: 40))
        
        let movieURLs = [
            // Netflix
            "https://www.netflix.com/title/80057281",
            "https://www.netflix.com/watch/80057281",
            "https://www.netflix.com/browse?jbv=80057281",
            
            // Amazon Prime
            "https://www.amazon.com/Inception-Leonardo-DiCaprio/dp/B0047WJ11G",
            "https://www.amazon.com/gp/video/detail/B0047WJ11G",
            "https://www.primevideo.com/detail/0HBID0ZQCNJ2GDZWFP7NW0S1LW",
            
            // YouTube
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            "https://youtu.be/dQw4w9WgXcQ",
            
            // IMDB
            "https://www.imdb.com/title/tt0468569/",
            "https://www.imdb.com/title/tt0468569/fullcredits",
            
            // Other streaming services
            "https://www.hulu.com/movie/parasite-4eb8486a-116f-46b5-9b0e-b3b27a0c8f9a",
            "https://www.disneyplus.com/movies/avengers-endgame/aRbVJUb2h2Rf",
            "https://play.hbomax.com/feature/urn:hbo:feature:GXdu2ZAglVqLCHAEAADbA",
            "https://tv.apple.com/us/movie/coda/umc.cmc.3eh9r5iz32ggdm4ccvw5igiir",
            
            // Movie websites
            "https://www.rottentomatoes.com/m/the_dark_knight",
            "https://www.metacritic.com/movie/the-dark-knight",
            "https://letterboxd.com/film/the-dark-knight/"
        ]
        
        testURLs(movieURLs, category: "Movies to Watch")
    }
    
    static func testShoppingURLs() {
        print("\n🛒 Testing Shopping URLs")
        print(String(repeating: "-", count: 40))
        
        let shoppingURLs = [
            // Amazon
            "https://www.amazon.com/Apple-iPhone-13-Pro-Max/dp/B09G9HD6PD",
            "https://www.amazon.com/dp/B09G9HD6PD",
            "https://www.amazon.com/Apple-iPhone-13-Pro-Max-128GB/dp/B09G9HD6PD/ref=sr_1_3",
            
            // eBay
            "https://www.ebay.com/itm/304195888302",
            "https://www.ebay.com/itm/304195888302?hash=item46d3928c1e:g:K2sAAOSwH4dhWg7I",
            "https://www.ebay.com/itm/wireless-bluetooth-headphones-noise-cancelling/304195888302",
            
            // Walmart
            "https://www.walmart.com/ip/Nintendo-Switch-OLED-Model-White/651391307",
            "https://www.walmart.com/ip/651391307",
            
            // Best Buy
            "https://www.bestbuy.com/site/macbook-pro-14-laptop-apple-m1-pro-chip-16gb-memory-512gb-ssd-space-gray/6450853.p",
            "https://www.bestbuy.com/site/6450853.p",
            
            // Target
            "https://www.target.com/p/nintendo-switch-oled-model-with-white-joy-con/-/**********",
            "https://www.target.com/p/**********",
            
            // Other shopping sites
            "https://www.etsy.com/listing/1234567890/handmade-wooden-cutting-board-walnut",
            "https://www.newegg.com/p/N82E16824012046",
            "https://www.homedepot.com/p/DEWALT-20-Volt-MAX-Cordless-Drill-Combo-Kit-2-Tool-with-2-Batteries-1-3Ah-Charger-and-Bag-DCK240C2/204373168",
            "https://www.wayfair.com/furniture/pdp/wade-logan-adalberto-tv-stand-for-tvs-up-to-78-w004159834.html"
        ]
        
        testURLs(shoppingURLs, category: "Shopping")
    }
    
    static func testBookURLs() {
        print("\n📚 Testing Book URLs")
        print(String(repeating: "-", count: 40))
        
        let bookURLs = [
            // Amazon Books
            "https://www.amazon.com/Atomic-Habits-Proven-Build-Break/dp/0735211299",
            "https://www.amazon.com/dp/0735211299",
            "https://www.amazon.com/Atomic-Habits-James-Clear/dp/0735211299/ref=sr_1_1",
            
            // Goodreads
            "https://www.goodreads.com/book/show/40121378-atomic-habits",
            "https://www.goodreads.com/en/book/show/40121378",
            
            // Barnes & Noble
            "https://www.barnesandnoble.com/w/atomic-habits-james-clear/1129201155",
            "https://www.barnesandnoble.com/w/1129201155",
            
            // Other book sites
            "https://books.google.com/books?id=XfFvDwAAQBAJ",
            "https://www.thriftbooks.com/w/atomic-habits-an-easy--proven-way-to-build-good-habits--break-bad-ones_james-clear/19162325/",
            "https://www.abebooks.com/9780735211292/Atomic-Habits-Easy-Proven-Way-0735211299/plp",
            "https://bookshop.org/books/atomic-habits-an-easy-proven-way-to-build-good-habits-break-bad-ones/9780735211292"
        ]
        
        testURLs(bookURLs, category: "To-Read")
    }
    
    static func testGenericURLs() {
        print("\n🌐 Testing Generic URLs")
        print(String(repeating: "-", count: 40))
        
        let genericURLs = [
            // News sites
            "https://www.nytimes.com/2023/06/25/technology/ai-progress-report.html",
            "https://www.bbc.com/news/technology-65231968",
            "https://www.cnn.com/2023/06/25/tech/apple-vision-pro-analysis/index.html",
            
            // Social media
            "https://twitter.com/elonmusk/status/1673465281371394049",
            "https://www.instagram.com/p/Ct7XYZ9OJKm/",
            "https://www.linkedin.com/posts/satyanadella_microsoft-build-2023-activity-7066465500255420416-iGSx",
            
            // Tech documentation
            "https://developer.apple.com/documentation/swiftui/view",
            "https://docs.microsoft.com/en-us/azure/ai-services/",
            "https://reactjs.org/docs/getting-started.html",
            
            // Misc
            "https://github.com/apple/swift",
            "https://stackoverflow.com/questions/24003191/get-device-name-in-swift",
            "https://en.wikipedia.org/wiki/Artificial_intelligence"
        ]
        
        testURLs(genericURLs, category: "To-Do")
    }
    
    static func testEdgeCaseURLs() {
        print("\n⚠️ Testing Edge Case URLs")
        print(String(repeating: "-", count: 40))
        
        let edgeCaseURLs = [
            // Malformed URLs
            "http://",
            "https://",
            "www.example.com",
            
            // URLs with special characters
            "https://example.com/path with spaces",
            "https://example.com/path%20with%20encoded%20spaces",
            "https://example.com/path?q=query&param=value",
            "https://example.com/path#fragment",
            
            // Very long URLs
            "https://example.com/" + String(repeating: "very-long-path-segment-", count: 10),
            
            // URLs with unusual TLDs
            "https://example.co.uk/path",
            "https://example.io/path",
            "https://example.dev/path",
            
            // URLs with subdomains
            "https://subdomain.example.com/path",
            "https://sub.sub.example.com/path",
            
            // URLs with ports
            "https://example.com:8080/path",
            "http://localhost:3000/path"
        ]
        
        testURLs(edgeCaseURLs, category: "To-Do")
    }
    
    static func testURLs(_ urls: [String], category: String) {
        let messageParser = MessageParser()
        
        for (index, url) in urls.enumerated() {
            print("\n\(index + 1). Testing URL: \(url)")
            
            let parsed = MessageParser.parseURL(url, for: category)
            
            print("   Main: \"\(parsed.mainMessage)\"")
            if let subcontent = parsed.subcontent {
                print("   Sub: \"\(subcontent)\"")
            }
            
            // Validate results
            let isValid = validateParsedURL(parsed, url: url, category: category)
            print("   Result: \(isValid ? "✅ VALID" : "❌ INVALID")")
            
            if !isValid {
                print("   ⚠️ Failed to extract meaningful information")
            }
        }
    }
    
    static func validateParsedURL(_ parsed: MessageParser.ParsedMessage, url: String, category: String) -> Bool {
        // Check if main message is not empty and not just the URL
        guard !parsed.mainMessage.isEmpty else { return false }
        guard parsed.mainMessage != url else { return false }
        
        // Check if main message is not too generic
        let genericTerms = ["Link", "URL", "Website"]
        if genericTerms.contains(where: { parsed.mainMessage.contains($0) }) {
            // If it contains a generic term, it should also contain something specific
            let hasSpecificInfo = parsed.mainMessage.count > 15
            if !hasSpecificInfo { return false }
        }
        
        // Category-specific validation
        switch category.lowercased() {
        case let cat where cat.contains("movie") || cat.contains("watch"):
            // For movies, main message shouldn't be just "Movie"
            if parsed.mainMessage == "Movie" { return false }
            
        case let cat where cat.contains("shop"):
            // For shopping, main message shouldn't be just "Product"
            if parsed.mainMessage == "Product" { return false }
            
        case let cat where cat.contains("book") || cat.contains("read"):
            // For books, main message shouldn't be just "Book"
            if parsed.mainMessage == "Book" { return false }
            
        default:
            break
        }
        
        return true
    }
    
    // MARK: - Integration Test
    
    static func testURLParsingIntegration() {
        print("\n🔄 Testing URL Parsing Integration")
        print(String(repeating: "-", count: 40))
        
        // Create a category manager
        let categoryManager = CategoryManager()
        
        // Add test categories
        let moviesCategory = categoryManager.addRootCategory(name: "Movies to Watch", sfSymbol: "film")
        let shoppingCategory = categoryManager.addRootCategory(name: "Shopping", sfSymbol: "cart")
        let booksCategory = categoryManager.addRootCategory(name: "To-Read", sfSymbol: "book")
        
        // Test URLs
        let testURLs = [
            "https://www.amazon.com/Inception-Leonardo-DiCaprio/dp/B0047WJ11G",
            "https://www.amazon.com/Apple-iPhone-13-Pro-Max/dp/B09G9HD6PD",
            "https://www.goodreads.com/book/show/40121378-atomic-habits"
        ]
        
        // Add URLs to appropriate categories
        moviesCategory.addMessage(testURLs[0])
        shoppingCategory.addMessage(testURLs[1])
        booksCategory.addMessage(testURLs[2])
        
        // Verify the messages were parsed correctly
        print("\nVerifying message parsing in categories:")
        
        for category in [moviesCategory, shoppingCategory, booksCategory] {
            print("\n\(category.name):")
            for message in category.messages {
                print("   Main: \"\(message.mainMessage)\"")
                if let subcontent = message.subcontent {
                    print("   Sub: \"\(subcontent)\"")
                }
                
                // Validate
                let isValid = !message.mainMessage.isEmpty && 
                              message.mainMessage != message.text &&
                              message.mainMessage != "Movie" &&
                              message.mainMessage != "Product" &&
                              message.mainMessage != "Book"
                
                print("   Result: \(isValid ? "✅ VALID" : "❌ INVALID")")
            }
        }
    }
}

// To run the tests: URLParsingTests.runAllURLParsingTests()
