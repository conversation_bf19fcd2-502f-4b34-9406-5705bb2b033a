//
//  ModernUITestView.swift
//  Pebl
//
//  Created by AI Assistant on 6/18/25.
//

import <PERSON><PERSON>
@testable import Pebl

/// Test view to demonstrate the modern UI and message parsing
struct ModernUITestView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            MessageParsingDemoView()
                .tabItem {
                    Image(systemName: "text.bubble")
                    Text("Parsing Demo")
                }
                .tag(0)
            
            ModernUIComparisonView()
                .tabItem {
                    Image(systemName: "square.and.pencil")
                    Text("UI Comparison")
                }
                .tag(1)
            
            LiveTestView()
                .tabItem {
                    Image(systemName: "play.circle")
                    Text("Live Test")
                }
                .tag(2)
        }
    }
}

/// Comparison between old and new UI
struct ModernUIComparisonView: View {
    let sampleMessages = [
        Message(text: "Watch The Dark Knight recommended by <PERSON>", categoryName: "Movies to Watch"),
        Message(text: "Buy iPhone 15 Pro from Apple Store", categoryName: "Shopping"),
        Message(text: "Read Atomic Habits by <PERSON>", categoryName: "To-Read"),
        Message(text: "Complete quarterly report by Friday urgent", categoryName: "To-Do")
    ]
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Modern UI Section
                    VStack(alignment: .leading, spacing: 12) {
                        Text("✨ Modern UI (New)")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                        
                        Text("Square checkboxes, clean main/sub content separation")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        VStack(spacing: 8) {
                            ForEach(sampleMessages, id: \.id) { message in
                                ModernMessageView(
                                    message: .constant(message),
                                    onToggle: { },
                                    showTimestamp: false
                                )
                            }
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color(.systemBackground))
                            .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                    )
                    
                    // Old UI Section (for comparison)
                    VStack(alignment: .leading, spacing: 12) {
                        Text("📱 Old UI (Previous)")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.secondary)
                        
                        Text("Round checkboxes, full message text")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        VStack(spacing: 8) {
                            ForEach(sampleMessages, id: \.id) { message in
                                OldStyleMessageView(message: .constant(message)) { }
                            }
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color(.systemGray6))
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(Color(.systemGray4), lineWidth: 1)
                            )
                    )
                    
                    // Benefits Section
                    VStack(alignment: .leading, spacing: 8) {
                        Text("🎯 Modern UI Benefits")
                            .font(.title3)
                            .fontWeight(.semibold)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            BenefitRow(icon: "checkmark.square", text: "Modern square checkboxes")
                            BenefitRow(icon: "text.alignleft", text: "Clean main message extraction")
                            BenefitRow(icon: "text.below.photo", text: "Contextual subcontent")
                            BenefitRow(icon: "eye", text: "Improved readability")
                            BenefitRow(icon: "rectangle.stack", text: "Uniform list appearance")
                            BenefitRow(icon: "sparkles", text: "Smooth animations")
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.accentColor.opacity(0.1))
                    )
                }
                .padding()
            }
            .navigationTitle("UI Comparison")
        }
    }
}

/// Old style message view for comparison
struct OldStyleMessageView: View {
    @Binding var message: Message
    let onToggle: () -> Void
    
    var body: some View {
        HStack {
            Button(action: {
                message.isCompleted.toggle()
                onToggle()
            }) {
                Image(systemName: message.isCompleted ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(message.isCompleted ? .green : .gray)
            }
            .buttonStyle(PlainButtonStyle())

            Text(message.text)
                .strikethrough(message.isCompleted)
                .foregroundColor(message.isCompleted ? .gray : .primary)

            Spacer()
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color(.systemBackground))
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color(.systemGray5), lineWidth: 1)
        )
    }
}

/// Benefit row component
struct BenefitRow: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .foregroundColor(.accentColor)
                .frame(width: 16)
            
            Text(text)
                .font(.subheadline)
                .foregroundColor(.primary)
            
            Spacer()
        }
    }
}

/// Live test view for trying out the parsing
struct LiveTestView: View {
    @State private var inputMessage = ""
    @State private var selectedCategory = "Movies to Watch"
    @State private var parsedMessage: Message?
    
    let categories = ["Movies to Watch", "Shopping", "To-Read", "To-Do", "Appointments"]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Test Message Parsing")
                        .font(.headline)
                    
                    TextField("Enter a message to parse...", text: $inputMessage)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                    
                    Picker("Category", selection: $selectedCategory) {
                        ForEach(categories, id: \.self) { category in
                            Text(category).tag(category)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    
                    Button("Parse Message") {
                        if !inputMessage.isEmpty {
                            parsedMessage = Message(text: inputMessage, categoryName: selectedCategory)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(inputMessage.isEmpty)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                )
                
                if let parsedMessage = parsedMessage {
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Parsed Result")
                            .font(.headline)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("Original:")
                                    .fontWeight(.medium)
                                Spacer()
                            }
                            Text(parsedMessage.text)
                                .padding(8)
                                .background(Color(.systemGray5))
                                .cornerRadius(8)
                            
                            HStack {
                                Text("Main Message:")
                                    .fontWeight(.medium)
                                Spacer()
                            }
                            Text(parsedMessage.mainMessage)
                                .padding(8)
                                .background(Color.green.opacity(0.1))
                                .cornerRadius(8)
                            
                            if let subcontent = parsedMessage.subcontent {
                                HStack {
                                    Text("Subcontent:")
                                        .fontWeight(.medium)
                                    Spacer()
                                }
                                Text(subcontent)
                                    .padding(8)
                                    .background(Color.blue.opacity(0.1))
                                    .cornerRadius(8)
                            }
                        }
                        
                        Text("Modern UI Preview")
                            .font(.headline)
                            .padding(.top)
                        
                        ModernMessageView(
                            message: .constant(parsedMessage),
                            onToggle: { },
                            showTimestamp: false
                        )
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemBackground))
                            .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                    )
                }
                
                Spacer()
                
                // Quick test examples
                VStack(alignment: .leading, spacing: 8) {
                    Text("Quick Test Examples")
                        .font(.headline)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach([
                                "Watch The Dark Knight recommended by John",
                                "Buy iPhone 15 Pro from Apple Store",
                                "Read Atomic Habits by James Clear",
                                "Complete report by Friday urgent",
                                "Schedule dentist appointment with Dr. Smith"
                            ], id: \.self) { example in
                                Button(example) {
                                    inputMessage = example
                                }
                                .font(.caption)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(Color.accentColor.opacity(0.1))
                                .foregroundColor(.accentColor)
                                .cornerRadius(8)
                            }
                        }
                        .padding(.horizontal)
                    }
                }
            }
            .padding()
            .navigationTitle("Live Test")
        }
    }
}

#Preview {
    ModernUITestView()
}
