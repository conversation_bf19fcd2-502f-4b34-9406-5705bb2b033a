//
//  SimpleAIModelTests.swift
//  Pebl
//
//  Created by AI Assistant on 6/17/25.
//

import Foundation
@testable import Pebl

/// Simple test class to demonstrate the clean, simplified AI categorization
class SimpleAIModelTests {

    static func runTests() {
        print("🧪 Testing Simplified AI Categorization System")
        print(String(repeating: "=", count: 50))

        let aiModel = AIModel()
        let categoryManager = CategoryManager()

        // Add some default categories for testing
        let defaultCategories = [
            ("To-Do", "checkmark.square"),
            ("Shopping", "cart"),
            ("Movies to Watch", "film"),
            ("To-Read", "book.circle")
        ]

        for (name, symbol) in defaultCategories {
            _ = categoryManager.addRootCategory(name: name, sfSymbol: symbol)
        }

        // Test messages
        let testMessages = [
            // Movies
            "Watch The Dark Knight - Batman action movie",
            "The Hangover - hilarious comedy",
            "Titanic - romantic drama",

            // Shopping
            "Buy iPhone 15 Pro",
            "Order Nike running shoes",
            "Get groceries for dinner",

            // URLs
            "https://www.amazon.com/dp/B08N5WRWNW - AirPods Pro",
            "https://www.netflix.com/title/80057281 - Stranger Things",

            // Tasks
            "URGENT: Submit quarterly report by Friday",
            "Schedule dentist appointment",
            "Plan weekend trip to San Francisco",

            // Creative projects
            "Research character development for my novel",
            "Schedule house viewing for Saturday",
            "Buy prenatal vitamins"
        ]

        print("\n📝 Testing Message Categorization:")
        print(String(repeating: "-", count: 30))

        for (index, message) in testMessages.enumerated() {
            print("\n\(index + 1). Message: \"\(message)\"")

            // Test categorization
            let availableCategories = categoryManager.getAllCategoryNames()
            let category = aiModel.categorizeMessage(message, availableCategories: availableCategories)
            print("   ✅ Category: \(category)")

            // Test URL analysis if message contains URLs
            let urls = aiModel.extractURLsFromMessage(message)
            if !urls.isEmpty {
                for url in urls {
                    let urlCategory = aiModel.analyzeURLContent(url)
                    print("   🔗 URL Category: \(urlCategory)")
                }
            }

            // Test subfolder suggestion (simulate category with enough messages)
            if let existingCategory = categoryManager.findCategory(named: category) {
                // Add some dummy messages to meet the 5-message threshold
                for i in 1...5 {
                    existingCategory.addMessage("Dummy message \(i)")
                }

                let subfolderResult = aiModel.shouldCreateSubfolder(for: message, in: existingCategory)
                if subfolderResult.shouldCreate, let subfolderName = subfolderResult.subfolderName {
                    print("   📁 Suggested Subfolder: \(subfolderName)")
                }
            }
        }
        
        print("\n\n🎯 Key Features Demonstrated:")
        print(String(repeating: "-", count: 40))
        print("✅ Simple, clean code (under 200 lines)")
        print("✅ No complex protocols or dependency injection")
        print("✅ No caching layer (keeps it simple)")
        print("✅ Basic configuration in code (easy to understand)")
        print("✅ AI-powered URL analysis (no hardcoded website lists)")
        print("✅ Dynamic subfolder creation")
        print("✅ Intelligent categorization with context")

        print("\n\n📋 Simple Subfolder Patterns:")
        print(String(repeating: "-", count: 35))
        print("• Movies: Comedy, Action & Adventure, Drama & Romance, Horror & Thriller, Documentaries")
        print("• Shopping: Clothing & Fashion, Electronics, Books, Food & Grocery, Home & Garden")
        print("• Tasks: Urgent, Work, Personal")
        print("• Reading: Articles & Blogs, Books, Research & Papers")

        print("\n\n🌐 AI-Powered URL Analysis:")
        print(String(repeating: "-", count: 30))
        print("• No hardcoded website lists")
        print("• Works with any URL automatically")
        print("• Categories: shopping, entertainment, news, learning, work, health, travel, etc.")

        print("\n\n💡 Usage Example:")
        print(String(repeating: "-", count: 20))
        print("let aiModel = AIModel()")
        print("let category = aiModel.categorizeMessage(\"Buy groceries\", availableCategories: [\"Shopping\", \"To-Do\"])")
        print("// Returns: \"Shopping\"")

        print("\n\n🚀 Perfect for Trial Apps:")
        print(String(repeating: "-", count: 25))
        print("• Easy to understand and modify")
        print("• No over-engineering")
        print("• All functionality in one clean file")
        print("• Ready to use immediately")
    }
}
