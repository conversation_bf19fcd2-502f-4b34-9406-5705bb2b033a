//
//  ModernMessageViewToggleTest.swift
//  PeblTests
//
//  Created by AI Assistant on 7/2/25.
//

import XCTest
import SwiftUI
@testable import Pebl

class ModernMessageViewToggleTest: XCTestCase {
    
    func testImmediateUIUpdate() {
        // Create a test message
        let testMessage = Message(
            text: "Test message",
            isCompleted: false,
            categoryName: "Test"
        )
        
        // Create a binding to the message
        let messageBinding = Binding<Message>(
            get: { testMessage },
            set: { _ in }
        )
        
        // Track if toggle was called
        var toggleCalled = false
        
        // Create the view
        let view = ModernMessageView(
            message: messageBinding,
            onToggle: {
                toggleCalled = true
            },
            showTimestamp: false
        )
        
        // This test verifies that the view can be created without crashing
        // and that the local state management is properly set up
        XCTAssertNotNil(view)
        
        // Verify initial state
        XCTAssertFalse(testMessage.isCompleted)
        XCTAssertFalse(toggleCalled)
    }
    
    func testLocalStateInitialization() {
        // Create a completed message
        let completedMessage = Message(
            text: "Completed message",
            isCompleted: true,
            categoryName: "Test"
        )
        
        let messageBinding = Binding<Message>(
            get: { completedMessage },
            set: { _ in }
        )
        
        let view = ModernMessageView(
            message: messageBinding,
            onToggle: { },
            showTimestamp: false
        )
        
        // Verify the view can handle completed messages
        XCTAssertNotNil(view)
        XCTAssertTrue(completedMessage.isCompleted)
    }
}
