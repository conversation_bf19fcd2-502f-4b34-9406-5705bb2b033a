//
//  ToggleFixVerificationTest.swift
//  Pebl
//
//  Created by AI Assistant on 7/2/25.
//

import SwiftUI
@testable import Pebl
import Combine

/// Simple test view to verify the toggle fix works consistently
struct ToggleFixVerificationTest: View {
    @StateObject private var testCategory = Category(name: "Toggle Test", sfSymbol: "checkmark.circle")
    @State private var cancellables = Set<AnyCancellable>()
    @State private var testResults: [String] = []
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Toggle Fix Verification")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Testing consistent toggle behavior across all messages")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                // Test results
                if !testResults.isEmpty {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Test Results:")
                            .font(.headline)
                        ForEach(testResults, id: \.self) { result in
                            Text(result)
                                .font(.caption)
                                .foregroundColor(result.contains("✅") ? .green : .red)
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
                }
                
                // Message list using the same pattern as the main app
                ScrollView {
                    LazyVStack(spacing: 8) {
                        ForEach($testCategory.messages) { $message in
                            ModernMessageView(
                                message: $message,
                                onToggle: {
                                    // Use the FIXED implementation - direct call to category method
                                    testCategory.toggleMessageCompletion(withId: message.id)
                                    logToggleResult(message: message)
                                },
                                showTimestamp: false
                            )
                        }
                    }
                    .padding(.horizontal, 16)
                }
                
                Spacer()
                
                // Test controls
                VStack(spacing: 12) {
                    Button("Add Test Messages") {
                        addTestMessages()
                    }
                    .buttonStyle(.borderedProminent)
                    
                    Button("Clear Results") {
                        testResults.removeAll()
                    }
                    .buttonStyle(.bordered)
                }
            }
            .padding()
            .navigationTitle("Toggle Fix Test")
            .onAppear {
                setupTest()
            }
        }
    }
    
    private func setupTest() {
        // Clear any existing messages
        testCategory.messages.removeAll()
        testResults.removeAll()
        
        // Add initial test messages
        addTestMessages()
        
        // Monitor category changes
        testCategory.objectWillChange
            .sink {
                print("📡 Category objectWillChange triggered")
            }
            .store(in: &cancellables)
    }
    
    private func addTestMessages() {
        let testMessages = [
            "Test message 1 - should toggle consistently",
            "Test message 2 - should toggle consistently", 
            "Test message 3 - should toggle consistently",
            "https://example.com - URL message test",
            "Test message 4 - should toggle consistently",
            "Test message 5 - should toggle consistently"
        ]
        
        for messageText in testMessages {
            testCategory.addMessage(messageText)
        }
        
        testResults.append("✅ Added \(testMessages.count) test messages")
    }
    
    private func logToggleResult(message: Message) {
        let status = message.isCompleted ? "completed" : "active"
        let result = "✅ Message '\(message.mainMessage)' toggled to: \(status)"
        testResults.append(result)
        print("🔄 \(result)")
    }
}

#Preview {
    ToggleFixVerificationTest()
}
