#!/usr/bin/env swift

// Simple test script to verify hashtag parsing fix
import Foundation

// Copy the relevant parts of HashtagParser for testing
class HashtagParser {
    
    struct HashtagParseResult {
        let cleanedMessage: String
        let categoryPath: CategoryPath?
        let originalMessage: String
    }
    
    struct CategoryPath {
        let category: String
        let subcategory: String?
        
        var fullPath: String {
            if let subcategory = subcategory {
                return "\(category)/\(subcategory)"
            }
            return category
        }
    }
    
    static func parseMessage(_ message: String) -> HashtagParseResult {
        let trimmedMessage = message.trimmingCharacters(in: .whitespacesAndNewlines)

        // Check for hashtag at the beginning
        if let result = parseHashtagAtBeginning(trimmedMessage, originalMessage: message) {
            return result
        }

        // Check for hashtag at the end
        if let result = parseHashtagAtEnd(trimmedMessage, originalMessage: message) {
            return result
        }

        // No hashtag found
        return HashtagParseResult(
            cleanedMessage: trimmedMessage,
            categoryPath: nil,
            originalMessage: message
        )
    }
    
    private static func parseHashtagAtBeginning(_ message: String, originalMessage: String) -> HashtagParseResult? {
        // Manual parsing approach for better control
        guard message.hasPrefix("#") else { return nil }

        let withoutHash = String(message.dropFirst()) // Remove the #

        // Find the first space that's not part of a category/subcategory name
        // We need to handle cases like "Home Improvement/Kitchen Remodel Install new cabinets"
        // where we want to extract "Home Improvement/Kitchen Remodel" as the path

        var categoryEnd = -1
        var hasSlash = withoutHash.contains("/")
        var wordCount = 0
        var afterSlash = false

        for (index, char) in withoutHash.enumerated() {
            if char == "/" {
                afterSlash = true
                wordCount = 0
            } else if char == " " {
                wordCount += 1

                // For patterns without slash (like "#Shopping Buy milk"),
                // the first space should end the category
                if !hasSlash && wordCount == 1 {
                    categoryEnd = index
                    break
                }

                // For patterns with slash, we allow 2 words per part
                if hasSlash {
                    if afterSlash && wordCount >= 2 {
                        categoryEnd = index
                        break
                    } else if !afterSlash && wordCount >= 2 {
                        categoryEnd = index
                        break
                    }
                }
            }
        }

        if categoryEnd == -1 {
            // No clear separation found
            return nil
        }

        let categoryPathString = String(withoutHash.prefix(categoryEnd)).trimmingCharacters(in: .whitespacesAndNewlines)
        let remainingMessage = String(withoutHash.dropFirst(categoryEnd + 1)).trimmingCharacters(in: .whitespacesAndNewlines)

        guard !categoryPathString.isEmpty && !remainingMessage.isEmpty,
              let categoryPath = parseCategoryPath(categoryPathString) else {
            return nil
        }

        return HashtagParseResult(
            cleanedMessage: remainingMessage,
            categoryPath: categoryPath,
            originalMessage: originalMessage
        )
    }
    
    private static func parseHashtagAtEnd(_ message: String, originalMessage: String) -> HashtagParseResult? {
        // Look for hashtag at end: message followed by space and #Category or #Category/Subcategory
        let pattern = #"^(.+)\s+#([^\s]+(?:\s+[^\s/]+)*(?:/[^\s]+(?:\s+[^\s]+)*)?)$"#

        guard let regex = try? NSRegularExpression(pattern: pattern, options: []),
              let match = regex.firstMatch(in: message, options: [], range: NSRange(location: 0, length: message.count)) else {
            return nil
        }

        let remainingMessage = String(message[Range(match.range(at: 1), in: message)!])
        let categoryPathString = String(message[Range(match.range(at: 2), in: message)!])

        guard let categoryPath = parseCategoryPath(categoryPathString) else {
            return nil
        }

        return HashtagParseResult(
            cleanedMessage: remainingMessage.trimmingCharacters(in: .whitespacesAndNewlines),
            categoryPath: categoryPath,
            originalMessage: originalMessage
        )
    }
    
    private static func parseCategoryPath(_ pathString: String) -> CategoryPath? {
        let trimmedPath = pathString.trimmingCharacters(in: .whitespacesAndNewlines)

        guard !trimmedPath.isEmpty else {
            return nil
        }

        let components = trimmedPath.components(separatedBy: "/")

        if components.count == 1 {
            let category = components[0].trimmingCharacters(in: .whitespacesAndNewlines)
            guard !category.isEmpty else {
                return nil
            }
            return CategoryPath(category: category, subcategory: nil)
        } else if components.count == 2 {
            let category = components[0].trimmingCharacters(in: .whitespacesAndNewlines)
            let subcategory = components[1].trimmingCharacters(in: .whitespacesAndNewlines)

            guard !category.isEmpty && !subcategory.isEmpty else {
                return nil
            }

            return CategoryPath(category: category, subcategory: subcategory)
        } else {
            return nil
        }
    }
}

// Test cases
func runTests() {
    print("🧪 Testing Hashtag Parser Fix...")
    
    // Test 1: Hashtag at end with multi-word category
    let test1 = "Buy organic vegetables #Grocery Shopping"
    let result1 = HashtagParser.parseMessage(test1)
    print("\nTest 1: '\(test1)'")
    print("  Cleaned message: '\(result1.cleanedMessage)'")
    print("  Category: '\(result1.categoryPath?.category ?? "nil")'")
    print("  Expected: 'Buy organic vegetables' -> 'Grocery Shopping'")
    print("  ✅ PASS" + (result1.cleanedMessage == "Buy organic vegetables" && result1.categoryPath?.category == "Grocery Shopping" ? "" : " ❌ FAIL"))
    
    // Test 2: Hashtag at beginning with multi-word category and subcategory
    let test2 = "#Home Improvement/Kitchen Remodel Install new cabinets"
    let result2 = HashtagParser.parseMessage(test2)
    print("\nTest 2: '\(test2)'")
    print("  Cleaned message: '\(result2.cleanedMessage)'")
    print("  Category: '\(result2.categoryPath?.category ?? "nil")'")
    print("  Subcategory: '\(result2.categoryPath?.subcategory ?? "nil")'")
    print("  Expected: 'Install new cabinets' -> 'Home Improvement/Kitchen Remodel'")
    print("  ✅ PASS" + (result2.cleanedMessage == "Install new cabinets" && result2.categoryPath?.category == "Home Improvement" && result2.categoryPath?.subcategory == "Kitchen Remodel" ? "" : " ❌ FAIL"))
    
    // Test 3: Single word category (should still work)
    let test3 = "#Shopping Buy milk"
    let result3 = HashtagParser.parseMessage(test3)
    print("\nTest 3: '\(test3)'")
    print("  Cleaned message: '\(result3.cleanedMessage)'")
    print("  Category: '\(result3.categoryPath?.category ?? "nil")'")
    print("  Expected: 'Buy milk' -> 'Shopping'")
    print("  ✅ PASS" + (result3.cleanedMessage == "Buy milk" && result3.categoryPath?.category == "Shopping" ? "" : " ❌ FAIL"))
    
    // Test 4: No hashtag (should return original message)
    let test4 = "Watch The Matrix"
    let result4 = HashtagParser.parseMessage(test4)
    print("\nTest 4: '\(test4)'")
    print("  Cleaned message: '\(result4.cleanedMessage)'")
    print("  Category: '\(result4.categoryPath?.category ?? "nil")'")
    print("  Expected: 'Watch The Matrix' -> nil")
    print("  ✅ PASS" + (result4.cleanedMessage == "Watch The Matrix" && result4.categoryPath == nil ? "" : " ❌ FAIL"))
    
    print("\n🎉 Hashtag Parser Fix Tests Complete!")
}

runTests()
