//
//  EditMessageTests.swift
//  PeblTests
//
//  Created by AI Assistant on 7/1/25.
//

import XCTest
@testable import Pebl
@testable import Pebl

class EditMessageTests: XCTestCase {
    
    var categoryManager: CategoryManager!
    var testCategory: Pebl.Category!
    
    override func setUp() {
        super.setUp()
        categoryManager = CategoryManager()
        testCategory = categoryManager.addRootCategory(name: "Test Category", sfSymbol: "folder.fill")
    }
    
    override func tearDown() {
        categoryManager = nil
        testCategory = nil
        super.tearDown()
    }
    
    func testMessageEditFunctionality() {
        // Test that we can edit a message and it updates correctly
        let originalText = "Original message text"
        let newText = "Edited message text"
        
        // Add a message to the category
        testCategory.addMessage(originalText)
        
        // Verify the message was added
        XCTAssertEqual(testCategory.messages.count, 1)
        let message = testCategory.messages.first!
        XCTAssertEqual(message.text, originalText)
        
        // Edit the message
        testCategory.editMessage(withId: message.id, newText: newText)
        
        // Verify the message was updated
        XCTAssertEqual(testCategory.messages.count, 1)
        let editedMessage = testCategory.messages.first!
        XCTAssertEqual(editedMessage.text, newText)
        XCTAssertEqual(editedMessage.id, message.id) // ID should remain the same
    }
    
    func testMessageUpdateTextMethod() {
        // Test the updateText method on Message struct
        var message = Message(text: "Original text", categoryName: "Test")
        let originalId = message.id
        
        // Update the text
        message.updateText("New text", for: "Test")
        
        // Verify the text was updated and parsed content was refreshed
        XCTAssertEqual(message.text, "New text")
        XCTAssertEqual(message.id, originalId) // ID should remain the same
        XCTAssertNotNil(message.mainMessage)
    }
    
    func testEditNonExistentMessage() {
        // Test editing a message that doesn't exist
        let randomId = UUID()
        let originalCount = testCategory.messages.count
        
        // Try to edit a non-existent message
        testCategory.editMessage(withId: randomId, newText: "New text")
        
        // Verify nothing changed
        XCTAssertEqual(testCategory.messages.count, originalCount)
    }
    
    func testCategoryCreationInMoveSheet() {
        // Test that we can create new categories
        let newCategoryName = "New Test Category"
        
        // Create a new category
        let newCategory = categoryManager.addRootCategory(name: newCategoryName, sfSymbol: "star.fill")
        
        // Verify the category was created
        XCTAssertNotNil(newCategory)
        XCTAssertEqual(newCategory.name, newCategoryName)
        XCTAssertEqual(newCategory.sfSymbol, "star.fill")
        XCTAssertTrue(categoryManager.rootCategories.contains { $0.id == newCategory.id })
    }
    
    func testMessageMoveBetweenCategories() {
        // Test moving a message from one category to another
        let sourceCategory = testCategory!
        let targetCategory = categoryManager.addRootCategory(name: "Target Category", sfSymbol: "target")
        
        // Add a message to source category
        sourceCategory.addMessage("Test message")
        let message = sourceCategory.messages.first!
        
        // Move the message to target category
        var updatedMessage = message
        updatedMessage.updateParsedContent(for: targetCategory.name)
        targetCategory.addMessage(updatedMessage)
        sourceCategory.removeMessage(withId: message.id)
        
        // Verify the message was moved
        XCTAssertEqual(sourceCategory.messages.count, 0)
        XCTAssertEqual(targetCategory.messages.count, 1)
        XCTAssertEqual(targetCategory.messages.first?.text, "Test message")
    }
    
    func testMessageParsingAfterEdit() {
        // Test that message parsing works correctly after editing
        let category = Pebl.Category(name: "Movies", sfSymbol: "film")
        
        // Add a movie message
        category.addMessage("Watch Inception")
        let message = category.messages.first!
        
        // Edit the message
        category.editMessage(withId: message.id, newText: "Watch The Dark Knight recommended by John")
        
        // Verify the edited message was parsed correctly
        let editedMessage = category.messages.first!
        XCTAssertEqual(editedMessage.text, "Watch The Dark Knight recommended by John")
        XCTAssertNotNil(editedMessage.mainMessage)
        XCTAssertNotNil(editedMessage.subcontent)
    }
}
