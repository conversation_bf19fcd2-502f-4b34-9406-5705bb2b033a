//
//  Category.swift
//  Pebl
//
//  Created by <PERSON><PERSON> on 6/17/25.
//

import Foundation

/// A hierarchical category that can contain subcategories and messages
class Category: ObservableObject, Codable, Identifiable {
    let id = UUID()
    @Published var name: String
    @Published var sfSymbol: String
    @Published var subcategories: [Category]
    @Published var messages: [Message]
    weak var parent: Category?
    weak var categoryManager: CategoryManager?
    
    init(name: String, sfSymbol: String? = nil, parent: Category? = nil, categoryManager: CategoryManager? = nil) {
        self.name = name
        self.sfSymbol = sfSymbol ?? DefaultCategoriesConfig.getSymbol(for: name)
        self.subcategories = []
        self.messages = []
        self.parent = parent
        self.categoryManager = categoryManager
    }
    
    // MARK: - Codable Implementation
    enum CodingKeys: String, CodingKey {
        case name, sfSymbol, subcategories, messages
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        name = try container.decode(String.self, forKey: .name)
        sfSymbol = try container.decode(String.self, forKey: .sfSymbol)
        subcategories = try container.decode([Category].self, forKey: .subcategories)

        // Handle both old format (array of strings) and new format (array of Message objects)
        if let messageObjects = try? container.decode([Message].self, forKey: .messages) {
            messages = messageObjects
        } else if let messageStrings = try? container.decode([String].self, forKey: .messages) {
            // Convert old string format to new Message format
            messages = messageStrings.map { Message(text: $0) }
        } else {
            messages = []
        }

        // Set parent relationships after decoding
        for subcategory in subcategories {
            subcategory.parent = self
            subcategory.categoryManager = categoryManager
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(name, forKey: .name)
        try container.encode(sfSymbol, forKey: .sfSymbol)
        try container.encode(subcategories, forKey: .subcategories)
        try container.encode(messages, forKey: .messages)
    }
    
    // MARK: - Category Management
    
    /// Add a new subcategory
    func addSubcategory(name: String, sfSymbol: String? = nil) -> Category {
        let newCategory = Category(name: name, sfSymbol: sfSymbol ?? DefaultCategoriesConfig.getSymbol(for: name), parent: self, categoryManager: categoryManager)
        subcategories.append(newCategory)
        notifyManagerOfChange()
        return newCategory
    }

    /// Add a new subcategory with automatic recategorization
    func addSubcategoryWithRecategorization(name: String,
                                          sfSymbol: String? = nil,
                                          completion: @escaping (SubcategoryAdditionResult) -> Void) {
        guard let categoryManager = categoryManager else {
            // Fallback to simple addition if no category manager
            let newCategory = addSubcategory(name: name, sfSymbol: sfSymbol)
            let result = SubcategoryAdditionResult(
                subcategory: newCategory,
                recategorization: RecategorizationResult(movedMessages: [], skippedCount: 0, newCategoriesCreated: []),
                success: true
            )
            completion(result)
            return
        }

        categoryManager.categorizationCoordinator.addSubcategory(
            name: name,
            sfSymbol: sfSymbol ?? DefaultCategoriesConfig.getSymbol(for: name),
            to: self,
            completion: completion
        )
    }
    
    /// Add a message to this category
    func addMessage(_ messageText: String) {
        let message = Message(text: messageText, categoryName: self.name)
        messages.append(message)
        notifyManagerOfChange()
    }

    /// Add a message object to this category
    func addMessage(_ message: Message) {
        var updatedMessage = message
        updatedMessage.updateParsedContent(for: self.name)
        messages.append(updatedMessage)
        notifyManagerOfChange()
    }

    /// Remove a subcategory
    func removeSubcategory(_ category: Category) {
        subcategories.removeAll { $0.id == category.id }
        notifyManagerOfChange()
    }

    /// Remove a message by ID
    func removeMessage(withId id: UUID) {
        messages.removeAll { $0.id == id }
        notifyManagerOfChange()
    }

    /// Edit a message by ID
    func editMessage(withId id: UUID, newText: String) {
        if let index = messages.firstIndex(where: { $0.id == id }) {
            // Trigger objectWillChange before modifying
            objectWillChange.send()
            messages[index].updateText(newText, for: self.name)
            notifyManagerOfChange()
        }
    }

    /// Toggle message completion status
    func toggleMessageCompletion(withId id: UUID) {
        if let index = messages.firstIndex(where: { $0.id == id }) {
            // Trigger objectWillChange before modifying
            objectWillChange.send()
            messages[index].isCompleted.toggle()
            notifyManagerOfChange()
        }
    }

    /// Remove all completed messages
    func removeCompletedMessages() {
        messages.removeAll { $0.isCompleted }
        notifyManagerOfChange()
    }

    /// Remove expired messages based on app settings
    func removeExpiredMessages(using settings: AppSettings) {
        let initialCount = messages.count
        messages.removeAll { settings.isMessageExpired($0) }
        let removedCount = initialCount - messages.count

        if removedCount > 0 {
            print("Removed \(removedCount) expired messages from category: \(name)")
            notifyManagerOfChange()
        }

        // Recursively remove expired messages from subcategories
        for subcategory in subcategories {
            subcategory.removeExpiredMessages(using: settings)
        }
    }

    /// Get count of expired messages in this category and subcategories
    func getExpiredMessageCount(using settings: AppSettings) -> Int {
        let expiredInThisCategory = messages.filter { settings.isMessageExpired($0) }.count
        let expiredInSubcategories = subcategories.reduce(0) { $0 + $1.getExpiredMessageCount(using: settings) }
        return expiredInThisCategory + expiredInSubcategories
    }

    /// Get all expired messages in this category and subcategories
    func getExpiredMessages(using settings: AppSettings) -> [Message] {
        var expiredMessages = messages.filter { settings.isMessageExpired($0) }
        for subcategory in subcategories {
            expiredMessages.append(contentsOf: subcategory.getExpiredMessages(using: settings))
        }
        return expiredMessages
    }

    /// Notify the category manager that something changed
    private func notifyManagerOfChange() {
        DispatchQueue.main.async {
            // Notify this category's observers first
            self.objectWillChange.send()
            // Then notify the category manager
            self.categoryManager?.objectWillChange.send()
            self.categoryManager?.saveToFile()
        }
    }

    // MARK: - Category Search and Navigation

    /// Find a category by name (recursive search)
    func findCategory(named name: String) -> Category? {
        if self.name == name {
            return self
        }

        for subcategory in subcategories {
            if let found = subcategory.findCategory(named: name) {
                return found
            }
        }

        return nil
    }

    /// Find a direct subcategory by name (non-recursive)
    func findSubcategory(named name: String) -> Category? {
        return subcategories.first { $0.name == name }
    }

    /// Get all category names in a flat array (for AI categorization)
    func getAllCategoryNames() -> [String] {
        var names = [name]
        for subcategory in subcategories {
            names.append(contentsOf: subcategory.getAllCategoryNames())
        }
        return names
    }

    /// Get the full path of this category (e.g., "Work/Projects/iOS")
    func getFullPath() -> String {
        if let parent = parent {
            return "\(parent.getFullPath())/\(name)"
        }
        return name
    }

    // MARK: - Statistics and Utilities

    /// Get total count of active (non-completed) messages in this category and all subcategories
    func getTotalMessageCount() -> Int {
        let activeMessages = messages.filter { !$0.isCompleted }.count
        let subcategoryCount = subcategories.reduce(0) { $0 + $1.getTotalMessageCount() }
        return activeMessages + subcategoryCount
    }

    /// Get total count of all messages (including completed) in this category and all subcategories
    func getTotalMessageCountIncludingCompleted() -> Int {
        let subcategoryCount = subcategories.reduce(0) { $0 + $1.getTotalMessageCountIncludingCompleted() }
        return messages.count + subcategoryCount
    }

    /// Check if this category is empty (no active messages and no subcategories with active messages)
    var isEmpty: Bool {
        return getTotalMessageCount() == 0
    }

    /// Move all messages from this category to other categories using AI categorization
    func redistributeMessages(using aiModel: AIModel, to rootCategories: [Category]) {
        let availableCategories = rootCategories.flatMap { $0.getAllCategoryNames() }

        for message in messages {
            let targetCategoryName = aiModel.categorizeMessage(message.text, availableCategories: availableCategories)

            // Find the target category and add the message
            for rootCategory in rootCategories {
                if let targetCategory = rootCategory.findCategory(named: targetCategoryName) {
                    targetCategory.addMessage(message)
                    break
                }
            }
        }

        // Recursively redistribute messages from subcategories
        for subcategory in subcategories {
            subcategory.redistributeMessages(using: aiModel, to: rootCategories)
        }

        // Clear this category
        messages.removeAll()
        subcategories.removeAll()
    }
}
