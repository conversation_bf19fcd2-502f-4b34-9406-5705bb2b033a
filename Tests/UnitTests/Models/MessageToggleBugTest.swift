//
//  MessageToggleBugTest.swift
//  Pebl
//
//  Created by AI Assistant on 6/18/25.
//

import XCTest
import Combine
@testable import Pebl

/// Test to verify the message toggle bug is fixed
class MessageToggleBugTest: XCTestCase {
    var categoryManager: CategoryManager!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        categoryManager = CategoryManager()
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        cancellables.removeAll()
        categoryManager = nil
        super.tearDown()
    }
    
    func testMessageToggleTriggersObjectWillChange() {
        print("🧪 Testing message toggle triggers objectWillChange...")
        
        // Create a category with messages
        let category = categoryManager.addRootCategory(name: "Test Category", sfSymbol: "folder.fill")
        category.addMessage("Task 1 - should toggle properly")
        category.addMessage("Task 2 - should toggle properly")
        category.addMessage("Task 3 - should toggle properly")
        
        XCTAssertEqual(category.messages.count, 3)
        
        // Track objectWillChange notifications
        var changeNotificationCount = 0
        category.objectWillChange
            .sink {
                changeNotificationCount += 1
                print("   📡 objectWillChange notification #\(changeNotificationCount)")
            }
            .store(in: &cancellables)
        
        // Test initial state
        XCTAssertFalse(category.messages[0].isCompleted)
        XCTAssertFalse(category.messages[1].isCompleted)
        XCTAssertFalse(category.messages[2].isCompleted)
        
        print("   Initial state: All messages are incomplete")
        
        // Toggle first message
        print("   🔄 Toggling first message...")
        category.toggleMessageCompletion(withId: category.messages[0].id)
        
        // Verify the change
        XCTAssertTrue(category.messages[0].isCompleted, "First message should be completed")
        XCTAssertFalse(category.messages[1].isCompleted, "Second message should remain incomplete")
        XCTAssertFalse(category.messages[2].isCompleted, "Third message should remain incomplete")
        
        // Toggle second message
        print("   🔄 Toggling second message...")
        category.toggleMessageCompletion(withId: category.messages[1].id)
        
        // Verify the change
        XCTAssertTrue(category.messages[0].isCompleted, "First message should remain completed")
        XCTAssertTrue(category.messages[1].isCompleted, "Second message should be completed")
        XCTAssertFalse(category.messages[2].isCompleted, "Third message should remain incomplete")
        
        // Toggle first message back
        print("   🔄 Toggling first message back...")
        category.toggleMessageCompletion(withId: category.messages[0].id)
        
        // Verify the change
        XCTAssertFalse(category.messages[0].isCompleted, "First message should be incomplete again")
        XCTAssertTrue(category.messages[1].isCompleted, "Second message should remain completed")
        XCTAssertFalse(category.messages[2].isCompleted, "Third message should remain incomplete")
        
        // Verify that objectWillChange was triggered for each toggle
        // We expect at least 3 notifications (one for each toggle)
        // Note: There might be additional notifications from notifyManagerOfChange
        XCTAssertGreaterThanOrEqual(changeNotificationCount, 3, 
                                   "Should have received at least 3 objectWillChange notifications")
        
        print("   ✅ Received \(changeNotificationCount) objectWillChange notifications")
        print("   ✅ All toggle operations worked correctly")
    }
    
    func testSubcategoryMessageToggle() {
        print("🧪 Testing subcategory message toggle...")
        
        // Create a category with subcategory
        let parentCategory = categoryManager.addRootCategory(name: "Parent Category", sfSymbol: "folder.fill")
        let subcategory = parentCategory.addSubcategory(name: "Sub Category", sfSymbol: "folder.badge.plus")
        
        // Add messages to subcategory
        subcategory.addMessage("Subtask 1 - should toggle properly")
        subcategory.addMessage("Subtask 2 - should toggle properly")
        
        XCTAssertEqual(subcategory.messages.count, 2)
        
        // Track objectWillChange notifications for subcategory
        var subcategoryChangeCount = 0
        subcategory.objectWillChange
            .sink {
                subcategoryChangeCount += 1
                print("   📡 Subcategory objectWillChange notification #\(subcategoryChangeCount)")
            }
            .store(in: &cancellables)
        
        // Test initial state
        XCTAssertFalse(subcategory.messages[0].isCompleted)
        XCTAssertFalse(subcategory.messages[1].isCompleted)
        
        print("   Initial state: All subcategory messages are incomplete")
        
        // Toggle first message in subcategory
        print("   🔄 Toggling first subcategory message...")
        subcategory.toggleMessageCompletion(withId: subcategory.messages[0].id)
        
        // Verify the change
        XCTAssertTrue(subcategory.messages[0].isCompleted, "First subcategory message should be completed")
        XCTAssertFalse(subcategory.messages[1].isCompleted, "Second subcategory message should remain incomplete")
        
        // Toggle second message in subcategory
        print("   🔄 Toggling second subcategory message...")
        subcategory.toggleMessageCompletion(withId: subcategory.messages[1].id)
        
        // Verify the change
        XCTAssertTrue(subcategory.messages[0].isCompleted, "First subcategory message should remain completed")
        XCTAssertTrue(subcategory.messages[1].isCompleted, "Second subcategory message should be completed")
        
        // Verify that objectWillChange was triggered
        XCTAssertGreaterThanOrEqual(subcategoryChangeCount, 2, 
                                   "Should have received at least 2 objectWillChange notifications for subcategory")
        
        print("   ✅ Received \(subcategoryChangeCount) subcategory objectWillChange notifications")
        print("   ✅ All subcategory toggle operations worked correctly")
    }
    
    func testToggleWithInvalidId() {
        print("🧪 Testing toggle with invalid ID...")
        
        let category = categoryManager.addRootCategory(name: "Test Category", sfSymbol: "folder.fill")
        category.addMessage("Valid message")
        
        let initialCompletionState = category.messages[0].isCompleted
        
        // Try to toggle with invalid ID
        let invalidId = UUID()
        category.toggleMessageCompletion(withId: invalidId)
        
        // Verify nothing changed
        XCTAssertEqual(category.messages[0].isCompleted, initialCompletionState, 
                      "Message state should not change with invalid ID")
        
        print("   ✅ Invalid ID toggle handled correctly")
    }
}
