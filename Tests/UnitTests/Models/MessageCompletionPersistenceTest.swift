//
//  MessageCompletionPersistenceTest.swift
//  PeblTests
//
//  Created by AI Assistant on 6/28/25.
//

import XCTest
@testable import Pebl

class MessageCompletionPersistenceTest: XCTestCase {

    var categoryStorageService: CategoryStorageService!

    override func setUp() {
        super.setUp()
        categoryStorageService = CategoryStorageService.shared
        // Clean up any existing test data
        clearTestData()
    }

    override func tearDown() {
        // Clean up test data after each test
        clearTestData()
        super.tearDown()
    }

    private func clearTestData() {
        // Clean up test files using storage service
        try? FileStorageService.shared.delete(AppConfig.categoriesFileName)

        // Clean up any backup files
        let backups = FileStorageService.shared.listBackups()
        for backup in backups {
            try? FileStorageService.shared.delete(backup)
        }
    }
    
    func testMessageCompletionStatePersistence() {
        print("🧪 Testing message completion state persistence...")

        // Create a fresh category manager
        let categoryManager = CategoryManager()
        let testCategory = categoryManager.addRootCategory(name: "Test Category", sfSymbol: "folder.fill")

        // Add test messages
        testCategory.addMessage("Task 1 - should be completed")
        testCategory.addMessage("Task 2 - should remain active")
        testCategory.addMessage("Task 3 - should be completed")

        print("📝 Added 3 test messages")

        // Mark some messages as completed using the proper method
        let message1Id = testCategory.messages[0].id
        let message3Id = testCategory.messages[2].id

        testCategory.toggleMessageCompletion(withId: message1Id)
        testCategory.toggleMessageCompletion(withId: message3Id)

        print("✅ Marked messages 1 and 3 as completed")

        // Verify completion state before persistence
        XCTAssertTrue(testCategory.messages[0].isCompleted, "Message 1 should be completed")
        XCTAssertFalse(testCategory.messages[1].isCompleted, "Message 2 should not be completed")
        XCTAssertTrue(testCategory.messages[2].isCompleted, "Message 3 should be completed")

        // Force save using storage service
        do {
            try categoryStorageService.saveCategories(from: categoryManager)
            print("💾 Saved to file using storage service")
        } catch {
            XCTFail("Failed to save categories: \(error)")
            return
        }

        // Create a new category manager to simulate app restart
        let newCategoryManager = CategoryManager()
        do {
            try categoryStorageService.loadCategories(into: newCategoryManager)
            print("🔄 Loaded from file with new category manager using storage service")
        } catch {
            XCTFail("Failed to load categories: \(error)")
            return
        }

        // Find the test category
        guard let loadedCategory = newCategoryManager.rootCategories.first(where: { $0.name == "Test Category" }) else {
            XCTFail("Test category not found after loading")
            return
        }

        // Verify that completion states are preserved
        XCTAssertEqual(loadedCategory.messages.count, 3, "Should have 3 messages after loading")

        // Find messages by text since IDs might be different
        let loadedMessage1 = loadedCategory.messages.first { $0.text == "Task 1 - should be completed" }
        let loadedMessage2 = loadedCategory.messages.first { $0.text == "Task 2 - should remain active" }
        let loadedMessage3 = loadedCategory.messages.first { $0.text == "Task 3 - should be completed" }

        XCTAssertNotNil(loadedMessage1, "Message 1 should exist after loading")
        XCTAssertNotNil(loadedMessage2, "Message 2 should exist after loading")
        XCTAssertNotNil(loadedMessage3, "Message 3 should exist after loading")

        // Verify completion states are preserved
        XCTAssertTrue(loadedMessage1?.isCompleted ?? false, "Message 1 should still be completed after loading")
        XCTAssertFalse(loadedMessage2?.isCompleted ?? true, "Message 2 should still be active after loading")
        XCTAssertTrue(loadedMessage3?.isCompleted ?? false, "Message 3 should still be completed after loading")

        print("✅ All completion states preserved correctly!")

        // Test toggling completion state again and verify persistence
        if let message2 = loadedMessage2 {
            loadedCategory.toggleMessageCompletion(withId: message2.id)

            // Save using storage service
            do {
                try categoryStorageService.saveCategories(from: newCategoryManager)
            } catch {
                XCTFail("Failed to save after toggle: \(error)")
                return
            }

            // Load again to verify the toggle was persisted
            let finalCategoryManager = CategoryManager()
            do {
                try categoryStorageService.loadCategories(into: finalCategoryManager)
            } catch {
                XCTFail("Failed to load after toggle: \(error)")
                return
            }

            guard let finalCategory = finalCategoryManager.rootCategories.first(where: { $0.name == "Test Category" }) else {
                XCTFail("Test category not found after second loading")
                return
            }

            let finalMessage2 = finalCategory.messages.first { $0.text == "Task 2 - should remain active" }
            XCTAssertTrue(finalMessage2?.isCompleted ?? false, "Message 2 should be completed after toggle and reload")

            print("✅ Toggle and persistence working correctly!")
        }
    }
    
    func testSubcategoryMessageCompletionPersistence() {
        print("🧪 Testing subcategory message completion state persistence...")

        // Create category with subcategory
        let categoryManager = CategoryManager()
        let parentCategory = categoryManager.addRootCategory(name: "Parent Category", sfSymbol: "folder.fill")
        let subcategory = parentCategory.addSubcategory(name: "Sub Category", sfSymbol: "folder.badge.plus")

        // Add messages to subcategory
        subcategory.addMessage("Subtask 1 - to be completed")
        subcategory.addMessage("Subtask 2 - to remain active")

        // Mark first message as completed
        subcategory.toggleMessageCompletion(withId: subcategory.messages[0].id)

        // Save using storage service
        do {
            try categoryStorageService.saveCategories(from: categoryManager)
        } catch {
            XCTFail("Failed to save subcategory test: \(error)")
            return
        }

        let newCategoryManager = CategoryManager()
        do {
            try categoryStorageService.loadCategories(into: newCategoryManager)
        } catch {
            XCTFail("Failed to load subcategory test: \(error)")
            return
        }

        // Verify subcategory completion state is preserved
        guard let loadedParent = newCategoryManager.rootCategories.first(where: { $0.name == "Parent Category" }),
              let loadedSub = loadedParent.subcategories.first(where: { $0.name == "Sub Category" }) else {
            XCTFail("Categories not found after loading")
            return
        }

        let loadedSubtask1 = loadedSub.messages.first { $0.text == "Subtask 1 - to be completed" }
        let loadedSubtask2 = loadedSub.messages.first { $0.text == "Subtask 2 - to remain active" }

        XCTAssertTrue(loadedSubtask1?.isCompleted ?? false, "Subtask 1 should be completed after loading")
        XCTAssertFalse(loadedSubtask2?.isCompleted ?? true, "Subtask 2 should be active after loading")

        print("✅ Subcategory completion states preserved correctly!")
    }


}
